#Mon Aug 25 21:57:25 CST 2025
com.example.castapp-main-46\:/drawable/crop_button_reset_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_crop_button_reset_background.xml.flat
com.example.castapp-main-46\:/drawable/ic_delete.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_delete.xml.flat
com.example.castapp-main-46\:/drawable/ic_remote_control.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_remote_control.xml.flat
com.example.castapp-main-46\:/drawable/edit_text_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_edit_text_background.xml.flat
com.example.castapp-main-46\:/layout/dialog_remote_receiver_settings_control.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_remote_receiver_settings_control.xml.flat
com.example.castapp-main-46\:/drawable/ic_font.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_font.xml.flat
com.example.castapp-main-46\:/drawable/ic_clear.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_clear.xml.flat
com.example.castapp-main-46\:/layout/dialog_add_media.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_add_media.xml.flat
com.example.castapp-main-46\:/drawable/ic_add_text.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_add_text.xml.flat
com.example.castapp-main-46\:/drawable/ic_palette.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_palette.xml.flat
com.example.castapp-main-46\:/drawable/ic_arrow_back.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_arrow_back.xml.flat
com.example.castapp-main-46\:/layout/dialog_font_size_settings.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_font_size_settings.xml.flat
com.example.castapp-main-46\:/layout/dialog_director.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_director.xml.flat
com.example.castapp-main-46\:/drawable/ic_upload.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_upload.xml.flat
com.example.castapp-main-46\:/layout/dialog_remote_layer_manager.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_remote_layer_manager.xml.flat
com.example.castapp-main-46\:/drawable/ic_info.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_info.xml.flat
com.example.castapp-main-46\:/drawable/ic_format_align_right.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_format_align_right.xml.flat
com.example.castapp-main-46\:/drawable/ic_add.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_add.xml.flat
com.example.castapp-main-46\:/drawable/dialog_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dialog_background.xml.flat
com.example.castapp-main-46\:/layout/spinner_font_size_item.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_spinner_font_size_item.xml.flat
com.example.castapp-main-46\:/drawable/button_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_button_background.xml.flat
com.example.castapp-main-46\:/layout/spinner_font_size_dropdown_item.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_spinner_font_size_dropdown_item.xml.flat
com.example.castapp-main-46\:/layout/fragment_sender_tab.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_sender_tab.xml.flat
com.example.castapp-main-46\:/drawable/edittext_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_edittext_background.xml.flat
com.example.castapp-main-46\:/layout/item_font_setting.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_font_setting.xml.flat
com.example.castapp-main-46\:/layout/floating_stopwatch_window.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_floating_stopwatch_window.xml.flat
com.example.castapp-main-46\:/layout/dialog_font_settings.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_font_settings.xml.flat
com.example.castapp-main-46\:/drawable/bottom_sheet_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bottom_sheet_background.xml.flat
com.example.castapp-main-46\:/drawable/ic_stopwatch.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_stopwatch.xml.flat
com.example.castapp-main-46\:/drawable/item_applied_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_item_applied_background.xml.flat
com.example.castapp-main-46\:/layout/dialog_line_spacing_settings.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_line_spacing_settings.xml.flat
com.example.castapp-main-46\:/drawable/ic_format_bold.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_format_bold.xml.flat
com.example.castapp-main-46\:/drawable/ic_add_media.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_add_media.xml.flat
com.example.castapp-main-46\:/drawable/ic_format_clear.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_format_clear.xml.flat
com.example.castapp-main-46\:/layout/fragment_receiver_tab.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_receiver_tab.xml.flat
com.example.castapp-main-46\:/layout/layout_text_edit_panel.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_layout_text_edit_panel.xml.flat
com.example.castapp-main-46\:/layout/dialog_note_edit.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_note_edit.xml.flat
com.example.castapp-main-46\:/drawable/ic_format_align_left.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_format_align_left.xml.flat
com.example.castapp-main-46\:/drawable/item_selected_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_item_selected_background.xml.flat
com.example.castapp-main-46\:/drawable/crop_button_apply_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_crop_button_apply_background.xml.flat
com.example.castapp-main-46\:/layout/item_remote_receiver.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_remote_receiver.xml.flat
com.example.castapp-main-46\:/drawable/ic_settings.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_settings.xml.flat
com.example.castapp-main-46\:/drawable/crop_button_cancel_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_crop_button_cancel_background.xml.flat
com.example.castapp-main-46\:/layout/item_line_spacing.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_line_spacing.xml.flat
com.example.castapp-main-46\:/drawable/ic_format_italic.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_format_italic.xml.flat
com.example.castapp-main-46\:/drawable/color_circle_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_color_circle_background.xml.flat
com.example.castapp-main-46\:/drawable/crop_control_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_crop_control_background.xml.flat
com.example.castapp-main-46\:/layout/item_remote_connection.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_remote_connection.xml.flat
com.example.castapp-main-46\:/layout/dialog_edit_layout.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_edit_layout.xml.flat
com.example.castapp-main-46\:/drawable/ic_cast.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_cast.xml.flat
com.example.castapp-main-46\:/layout/item_connection.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_connection.xml.flat
com.example.castapp-main-46\:/drawable/ic_drag_handle.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_drag_handle.xml.flat
com.example.castapp-main-46\:/drawable/ic_refresh.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_refresh.xml.flat
com.example.castapp-main-46\:/drawable/button_apply_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_button_apply_background.xml.flat
com.example.castapp-main-46\:/layout/item_color_palette.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_color_palette.xml.flat
com.example.castapp-main-46\:/layout/dialog_save_director_layout.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_save_director_layout.xml.flat
com.example.castapp-main-46\:/drawable/ic_send.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_send.xml.flat
com.example.castapp-main-46\:/layout/precision_control_panel.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_precision_control_panel.xml.flat
com.example.castapp-main-46\:/layout/item_remote_connection_control.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_remote_connection_control.xml.flat
com.example.castapp-main-46\:/drawable/item_normal_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_item_normal_background.xml.flat
com.example.castapp-main-46\:/layout/dialog_window_settings.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_window_settings.xml.flat
com.example.castapp-main-46\:/drawable/ic_close.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_close.xml.flat
com.example.castapp-main-46\:/drawable/floating_stopwatch_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_floating_stopwatch_background.xml.flat
com.example.castapp-main-46\:/drawable/palette.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_palette.xml.flat
com.example.castapp-main-46\:/drawable/button_cancel_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_button_cancel_background.xml.flat
com.example.castapp-main-46\:/drawable/ic_edit.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_edit.xml.flat
com.example.castapp-main-46\:/layout/dialog_remote_sender_control.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_remote_sender_control.xml.flat
com.example.castapp-main-46\:/layout/spinner_text_alignment_dropdown_item.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_spinner_text_alignment_dropdown_item.xml.flat
com.example.castapp-main-46\:/layout/item_window_settings.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_window_settings.xml.flat
com.example.castapp-main-46\:/layout/item_font_size_setting.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_font_size_setting.xml.flat
com.example.castapp-main-46\:/drawable/spinner_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_spinner_background.xml.flat
com.example.castapp-main-46\:/layout/item_letter_spacing.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_letter_spacing.xml.flat
com.example.castapp-main-46\:/drawable/button_reset_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_button_reset_background.xml.flat
com.example.castapp-main-46\:/layout/dialog_send.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_send.xml.flat
com.example.castapp-main-46\:/layout/spinner_letter_spacing_item.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_spinner_letter_spacing_item.xml.flat
com.example.castapp-main-46\:/layout/item_font_file.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_font_file.xml.flat
com.example.castapp-main-46\:/drawable/ic_layer.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_layer.xml.flat
com.example.castapp-main-46\:/layout/spinner_text_alignment_item.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_spinner_text_alignment_item.xml.flat
com.example.castapp-main-46\:/layout/dialog_save_options.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_save_options.xml.flat
com.example.castapp-main-46\:/drawable/precision_control_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_precision_control_background.xml.flat
com.example.castapp-main-46\:/drawable/ic_add_video.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_add_video.xml.flat
com.example.castapp-main-46\:/layout/spinner_line_spacing_item.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_spinner_line_spacing_item.xml.flat
com.example.castapp-main-46\:/layout/dialog_color_picker.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_color_picker.xml.flat
com.example.castapp-main-46\:/layout/dialog_remote_receiver_control.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_remote_receiver_control.xml.flat
com.example.castapp-main-46\:/drawable/count_badge_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_count_badge_background.xml.flat
com.example.castapp-main-46\:/drawable/item_selected_applied_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_item_selected_applied_background.xml.flat
com.example.castapp-main-46\:/drawable/info_card_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_info_card_background.xml.flat
com.example.castapp-main-46\:/drawable/button_delete_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_button_delete_background.xml.flat
com.example.castapp-main-46\:/drawable/ic_file.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_file.xml.flat
com.example.castapp-main-46\:/drawable/ic_format_align_center.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_format_align_center.xml.flat
com.example.castapp-main-46\:/layout/crop_control_buttons.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_crop_control_buttons.xml.flat
com.example.castapp-main-46\:/layout/item_director_info.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_director_info.xml.flat
com.example.castapp-main-46\:/drawable/circle_green.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_circle_green.xml.flat
com.example.castapp-main-46\:/layout/item_director_layout.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_director_layout.xml.flat
com.example.castapp-main-46\:/layout/dialog_receive.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_receive.xml.flat
com.example.castapp-main-46\:/drawable/connection_status_indicator.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_connection_status_indicator.xml.flat
com.example.castapp-main-46\:/drawable/ic_updated.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_updated.xml.flat
com.example.castapp-main-46\:/layout/activity_main.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
com.example.castapp-main-46\:/drawable/ic_check.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_check.xml.flat
com.example.castapp-main-46\:/drawable/ic_folder.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_folder.xml.flat
com.example.castapp-main-46\:/layout/dialog_remote_control_manager.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_remote_control_manager.xml.flat
com.example.castapp-main-46\:/drawable/ic_notification.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_notification.xml.flat
com.example.castapp-main-46\:/layout/dialog_add_receiver.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_add_receiver.xml.flat
com.example.castapp-main-46\:/drawable/ic_director.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_director.xml.flat
com.example.castapp-main-46\:/layout/spinner_line_spacing_dropdown_item.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_spinner_line_spacing_dropdown_item.xml.flat
com.example.castapp-main-46\:/drawable/rounded_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_rounded_background.xml.flat
com.example.castapp-main-46\:/layout/dialog_letter_spacing_settings.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_letter_spacing_settings.xml.flat
com.example.castapp-main-46\:/drawable/ic_add_camera.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_add_camera.xml.flat
com.example.castapp-main-46\:/drawable/ic_error.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_error.xml.flat
com.example.castapp-main-46\:/drawable/ic_window_settings.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_window_settings.xml.flat
com.example.castapp-main-46\:/layout/item_layer.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_layer.xml.flat
com.example.castapp-main-46\:/drawable/wheel.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_wheel.xml.flat
com.example.castapp-main-46\:/drawable/list_item_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_list_item_background.xml.flat
com.example.castapp-main-46\:/layout/dialog_layer_manager.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_layer_manager.xml.flat
com.example.castapp-main-46\:/layout/dialog_add_remote_device.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_add_remote_device.xml.flat
com.example.castapp-main-46\:/layout/spinner_letter_spacing_dropdown_item.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_spinner_letter_spacing_dropdown_item.xml.flat
com.example.castapp-main-46\:/drawable/button_primary_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_button_primary_background.xml.flat
com.example.castapp-main-46\:/layout/dialog_font_file_picker.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_font_file_picker.xml.flat
com.example.castapp-main-46\:/drawable/ic_save.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_save.xml.flat
com.example.castapp-main-46\:/drawable/ic_add_picture.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_add_picture.xml.flat
com.example.castapp-main-46\:/drawable/button_cancel_apply_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_button_cancel_apply_background.xml.flat
