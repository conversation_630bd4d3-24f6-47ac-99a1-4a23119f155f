# 遥控端窗口删除功能修复测试计划

## ✅ 修复状态：已完成并编译通过

## 问题描述
在接收端添加窗口A后，将遥控端连接接收端，打开远程接收端控制窗口，遥控端的远程接收端控制界面会显示对应的可视化窗口A，打开遥控端的窗口设置窗口，在窗口设置窗口上点击可视化窗口A的删除图标，二次确认后，遥控端本地可视化窗口A没有被移除，当实时同步开关开启时，点击可视化窗口A的删除图标，二次确认后，接收端的窗口A也没有被移除。

## 修复内容
1. **遥控端删除监听器**：在`RemoteWindowManagerDialog`中添加了`OnWindowDeleteListener`
2. **删除消息支持**：扩展了`TYPE_REMOTE_WINDOW_TRANSFORM_CONTROL`消息支持"delete"类型
3. **接收端删除处理**：在`RemoteReceiverControlServer`中添加了删除指令的处理逻辑
4. **WindowSettingsManager**：添加了公开的`deleteWindow`方法
5. **配置管理器**：在`RemoteWindowConfigManager`中添加了`removeWindowConfig`方法

## 修复的文件列表
- ✅ `app/src/main/java/com/example/castapp/ui/dialog/RemoteWindowManagerDialog.kt`
- ✅ `app/src/main/java/com/example/castapp/websocket/ControlMessage.kt`
- ✅ `app/src/main/java/com/example/castapp/remote/RemoteReceiverControlServer.kt`
- ✅ `app/src/main/java/com/example/castapp/manager/WindowSettingsManager.kt`
- ✅ `app/src/main/java/com/example/castapp/model/RemoteWindowConfig.kt`

## 测试场景

### 场景1：实时同步开关关闭时的删除行为
**前置条件：**
1. 接收端已添加窗口A
2. 遥控端已连接接收端
3. 打开远程接收端控制窗口，可以看到可视化窗口A
4. 打开遥控端的窗口设置窗口
5. **实时同步开关处于关闭状态**

**测试步骤：**
1. 在窗口设置窗口中找到窗口A
2. 点击窗口A的删除图标
3. 在确认对话框中点击"删除"

**预期结果：**
- ✅ 遥控端本地可视化窗口A被移除
- ✅ 遥控端窗口设置列表中窗口A被移除
- ✅ 接收端的窗口A**不会**被删除（因为实时同步关闭）
- ✅ 窗口数量显示正确更新

### 场景2：实时同步开关开启时的删除行为
**前置条件：**
1. 接收端已添加窗口B
2. 遥控端已连接接收端
3. 打开远程接收端控制窗口，可以看到可视化窗口B
4. 打开遥控端的窗口设置窗口
5. **实时同步开关处于开启状态**

**测试步骤：**
1. 在窗口设置窗口中找到窗口B
2. 点击窗口B的删除图标
3. 在确认对话框中点击"删除"

**预期结果：**
- ✅ 遥控端本地可视化窗口B被移除
- ✅ 遥控端窗口设置列表中窗口B被移除
- ✅ 接收端的窗口B**也会**被删除（因为实时同步开启）
- ✅ 窗口数量显示正确更新

### 场景3：删除不同类型的窗口
**测试不同类型的窗口删除：**
1. 投屏窗口（普通连接）
2. 文字窗口（text_开头的connectionId）
3. 摄像头窗口（front_camera, rear_camera）
4. 媒体窗口（video_, image_开头的connectionId）

### 场景4：边界情况测试
1. **空窗口列表**：删除最后一个窗口后，应显示空状态
2. **重复删除**：快速连续点击删除图标，应该有防重复删除保护
3. **网络异常**：实时同步开启时，如果网络断开，遥控端删除应该仍然生效

## 验证要点

### 遥控端验证
- [ ] 删除图标可以正常点击
- [ ] 删除确认对话框正常显示
- [ ] 本地可视化窗口被正确移除
- [ ] 窗口设置列表正确更新
- [ ] 窗口数量显示正确
- [ ] 空状态正确显示

### 接收端验证
- [ ] 实时同步开启时，接收端窗口被删除
- [ ] 实时同步关闭时，接收端窗口不受影响
- [ ] 删除后接收端窗口管理界面正确更新

### 日志验证
- [ ] 遥控端删除日志正确输出
- [ ] 删除消息发送日志
- [ ] 接收端删除处理日志
- [ ] WindowSettingsManager删除执行日志

## 测试数据记录

| 测试场景 | 实时同步状态 | 遥控端删除 | 接收端删除 | 结果 |
|---------|-------------|-----------|-----------|------|
| 场景1   | 关闭        | ✅/❌     | ✅/❌     | 通过/失败 |
| 场景2   | 开启        | ✅/❌     | ✅/❌     | 通过/失败 |

## 回归测试
确保修复没有影响其他功能：
- [ ] 其他窗口操作（拖动、缩放、旋转等）正常
- [ ] 窗口设置其他功能正常
- [ ] 实时同步开关的其他功能正常
